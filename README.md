# MSSQL to PostgreSQL Migrator

A powerful desktop application built with Electron and Node.js for migrating databases from Microsoft SQL Server to PostgreSQL.

## Features

### Core Migration Features
- **Database Connection Management**: Connect to both MSSQL source and PostgreSQL target databases
- **Object Discovery**: Automatically discover and list all database objects (tables, views, procedures, functions)
- **Selective Migration**: Choose specific objects to migrate with an intuitive table interface
- **Script Generation**: Generate PostgreSQL-compatible DDL scripts from MSSQL objects
- **Script Preview**: Review generated migration scripts before execution
- **Migration Execution**: Execute migration scripts directly on target PostgreSQL database

### User Interface Features
- **Modern UI**: Clean, professional interface built with Bootstrap 5
- **Search & Filter**: Search objects by name and filter by type
- **Manual Object Entry**: Add objects manually by name
- **Progress Tracking**: Real-time progress updates during migration
- **Connection Testing**: Test database connections before migration
- **Error Handling**: Comprehensive error reporting and logging

### Technical Features
- **Data Type Mapping**: Intelligent mapping of MSSQL data types to PostgreSQL equivalents
- **Constraint Migration**: Migrate primary keys, foreign keys, and indexes
- **Schema Support**: Full support for database schemas
- **Transaction Safety**: All migrations run within transactions for safety
- **Logging**: Detailed logging for troubleshooting and audit trails

## Installation

### Prerequisites
- Node.js (version 16 or higher)
- npm or yarn package manager

### Setup
1. Clone or download the project
2. Install dependencies:
   ```bash
   npm install
   ```

### Running the Application
```bash
# Development mode
npm run dev

# Production mode
npm start
```

### Building Executables
```bash
# Build for current platform
npm run build

# Build for Windows
npm run build-win

# Build for macOS
npm run build-mac

# Build for Linux
npm run build-linux
```

## Usage Guide

### 1. Database Connections

#### Source Database (MSSQL)
1. Enter your MSSQL server details:
   - **Server**: Server name or IP address
   - **Database**: Database name
   - **Username**: SQL Server username
   - **Password**: SQL Server password

2. Click "Test Connection" to verify connectivity

#### Target Database (PostgreSQL)
1. Enter your PostgreSQL server details:
   - **Host**: Server hostname or IP address
   - **Port**: Port number (default: 5432)
   - **Database**: Database name
   - **Username**: PostgreSQL username
   - **Password**: PostgreSQL password

2. Click "Test Connection" to verify connectivity

### 2. Object Discovery
1. After successful source connection test, click "Discover Objects"
2. The application will scan the source database and list all objects
3. Objects are displayed with:
   - Object name and row count (for tables)
   - Object type (Table, View, Procedure, Function)
   - Schema name
   - Migration status

### 3. Object Selection
- **Select All**: Use the checkbox in the header to select/deselect all objects
- **Individual Selection**: Check specific objects you want to migrate
- **Search**: Use the search box to find objects by name
- **Filter**: Filter objects by type using the dropdown
- **Manual Entry**: Add objects manually using the "Add Manual" button

### 4. Script Generation
1. Select the objects you want to migrate
2. Click "Generate Script"
3. The application will create PostgreSQL-compatible DDL scripts
4. Review the generated script in the "Generated Script" tab

### 5. Script Management
- **Copy**: Copy the script to clipboard
- **Save**: Save the script to a .sql file
- **Execute**: Run the script directly on the target PostgreSQL database

### 6. Migration Execution
1. Ensure target database connection is configured and tested
2. Review the generated script thoroughly
3. Click "Execute" to run the migration
4. Monitor progress and check for any errors

## Data Type Mapping

The application automatically maps MSSQL data types to PostgreSQL equivalents:

| MSSQL Type | PostgreSQL Type | Notes |
|------------|-----------------|-------|
| varchar(n) | VARCHAR(n) | Length preserved |
| nvarchar(n) | VARCHAR(n) | Unicode converted to UTF-8 |
| text/ntext | TEXT | Large text fields |
| int | INTEGER | 32-bit integer |
| bigint | BIGINT | 64-bit integer |
| smallint | SMALLINT | 16-bit integer |
| tinyint | SMALLINT | Mapped to SMALLINT |
| bit | BOOLEAN | Boolean values |
| decimal(p,s) | NUMERIC(p,s) | Precision and scale preserved |
| money | MONEY | Currency type |
| datetime | TIMESTAMP | Date and time |
| date | DATE | Date only |
| time | TIME | Time only |
| uniqueidentifier | UUID | Requires uuid-ossp extension |
| binary/varbinary | BYTEA | Binary data |

## Function Conversions

Common MSSQL functions are converted to PostgreSQL equivalents:

| MSSQL Function | PostgreSQL Function |
|----------------|-------------------|
| GETDATE() | CURRENT_TIMESTAMP |
| NEWID() | gen_random_uuid() |
| LEN() | LENGTH() |
| ISNULL() | COALESCE() |
| CHARINDEX() | POSITION() |
| DATEPART() | EXTRACT() |

## Troubleshooting

### Common Issues

1. **Connection Failures**
   - Verify server addresses and credentials
   - Check firewall settings
   - Ensure database services are running

2. **Object Discovery Issues**
   - Verify user permissions on source database
   - Check if database contains supported object types

3. **Script Generation Errors**
   - Review application logs in the `logs` folder
   - Check for unsupported data types or constructs

4. **Migration Execution Failures**
   - Ensure target database has required extensions (uuid-ossp, pgcrypto)
   - Verify user has CREATE permissions on target database
   - Check for naming conflicts

### Logging
Application logs are stored in the `logs` folder:
- `app.log`: General application logs
- `error.log`: Error-specific logs

### Support
For issues and feature requests, please check the application logs and provide detailed error messages.

## Development

### Project Structure
```
src/
├── main.js              # Electron main process
├── database/            # Database connection and migration logic
│   ├── mssql-connection.js
│   ├── postgresql-connection.js
│   ├── object-discovery.js
│   └── migration-script-generator.js
├── ui/                  # User interface
│   ├── index.html
│   ├── styles.css
│   └── app.js
└── utils/               # Utility modules
    └── logger.js
```

### Technologies Used
- **Electron**: Desktop application framework
- **Node.js**: Runtime environment
- **Bootstrap 5**: UI framework
- **jQuery**: DOM manipulation
- **mssql**: SQL Server client
- **pg**: PostgreSQL client
- **winston**: Logging library

## License

MIT License - see LICENSE file for details.

## Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.
