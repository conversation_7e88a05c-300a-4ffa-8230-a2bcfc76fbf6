{"source": {"server": "localhost", "database": "SampleDB", "username": "sa", "password": "your_password_here", "options": {"encrypt": false, "trustServerCertificate": true}}, "target": {"host": "localhost", "port": 5432, "database": "migrated_db", "username": "postgres", "password": "your_password_here"}, "migration": {"batchSize": 1000, "timeout": 30000, "createSchemas": true, "dropExisting": true, "migrateData": false, "objectTypes": ["table", "view", "procedure", "function"], "excludeSchemas": ["sys", "information_schema"], "includeIndexes": true, "includeForeignKeys": true}}