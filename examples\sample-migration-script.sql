-- =====================================================
-- MSSQL to PostgreSQL Migration Script
-- Generated on: 2024-12-19T10:30:00.000Z
-- =====================================================

-- Enable UUID extension if not exists
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Set client encoding
SET client_encoding = 'UTF8';

-- Begin transaction
BEGIN;

-- Table: dbo.Users
CREATE SCHEMA IF NOT EXISTS "dbo";

DROP TABLE IF EXISTS "dbo"."Users" CASCADE;

CREATE TABLE "dbo"."Users" (
    "UserID" INTEGER NOT NULL,
    "Username" VARCHAR(50) NOT NULL,
    "Email" VARCHAR(100) NOT NULL,
    "FirstName" VARCHAR(50),
    "LastName" VARCHAR(50),
    "DateCreated" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "IsActive" BOOLEAN DEFAULT true,
    "UserGUID" UUID DEFAULT gen_random_uuid(),
    CONSTRAINT "pk_Users" PRIMARY KEY ("UserID")
);

CREATE UNIQUE INDEX "IX_Users_Username" ON "dbo"."Users" ("Username");
CREATE INDEX "IX_Users_Email" ON "dbo"."Users" ("Email");

-- Table: dbo.Orders
CREATE SCHEMA IF NOT EXISTS "dbo";

DROP TABLE IF EXISTS "dbo"."Orders" CASCADE;

CREATE TABLE "dbo"."Orders" (
    "OrderID" INTEGER NOT NULL,
    "UserID" INTEGER NOT NULL,
    "OrderDate" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "TotalAmount" NUMERIC(10,2) NOT NULL,
    "Status" VARCHAR(20) DEFAULT 'Pending',
    "ShippingAddress" TEXT,
    CONSTRAINT "pk_Orders" PRIMARY KEY ("OrderID")
);

ALTER TABLE "dbo"."Orders" ADD CONSTRAINT "fk_Orders_UserID" FOREIGN KEY ("UserID") REFERENCES "dbo"."Users" ("UserID");

CREATE INDEX "IX_Orders_UserID" ON "dbo"."Orders" ("UserID");
CREATE INDEX "IX_Orders_OrderDate" ON "dbo"."Orders" ("OrderDate");

-- Table: dbo.Products
CREATE SCHEMA IF NOT EXISTS "dbo";

DROP TABLE IF EXISTS "dbo"."Products" CASCADE;

CREATE TABLE "dbo"."Products" (
    "ProductID" INTEGER NOT NULL,
    "ProductName" VARCHAR(100) NOT NULL,
    "Description" TEXT,
    "Price" NUMERIC(10,2) NOT NULL,
    "CategoryID" INTEGER,
    "InStock" BOOLEAN DEFAULT true,
    "CreatedDate" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "ModifiedDate" TIMESTAMP,
    CONSTRAINT "pk_Products" PRIMARY KEY ("ProductID")
);

CREATE INDEX "IX_Products_CategoryID" ON "dbo"."Products" ("CategoryID");
CREATE INDEX "IX_Products_Price" ON "dbo"."Products" ("Price");

-- View: dbo.ActiveUsers
CREATE SCHEMA IF NOT EXISTS "dbo";

DROP VIEW IF EXISTS "dbo"."ActiveUsers" CASCADE;

CREATE VIEW "dbo"."ActiveUsers" AS
SELECT 
    "UserID",
    "Username",
    "Email",
    "FirstName",
    "LastName",
    "DateCreated"
FROM "dbo"."Users"
WHERE "IsActive" = true;

-- View: dbo.OrderSummary
CREATE SCHEMA IF NOT EXISTS "dbo";

DROP VIEW IF EXISTS "dbo"."OrderSummary" CASCADE;

CREATE VIEW "dbo"."OrderSummary" AS
SELECT 
    u."UserID",
    u."Username",
    COUNT(o."OrderID") as "TotalOrders",
    COALESCE(SUM(o."TotalAmount"), 0) as "TotalSpent",
    MAX(o."OrderDate") as "LastOrderDate"
FROM "dbo"."Users" u
LEFT JOIN "dbo"."Orders" o ON u."UserID" = o."UserID"
WHERE u."IsActive" = true
GROUP BY u."UserID", u."Username";

-- Function: dbo.GetUserOrderCount
CREATE SCHEMA IF NOT EXISTS "dbo";

DROP FUNCTION IF EXISTS "dbo"."GetUserOrderCount" CASCADE;

CREATE OR REPLACE FUNCTION "dbo"."GetUserOrderCount"(user_id INTEGER)
RETURNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT COUNT(*)
        FROM "dbo"."Orders"
        WHERE "UserID" = user_id
    );
END;
$$ LANGUAGE plpgsql;

-- Function: dbo.CalculateOrderTotal
CREATE SCHEMA IF NOT EXISTS "dbo";

DROP FUNCTION IF EXISTS "dbo"."CalculateOrderTotal" CASCADE;

CREATE OR REPLACE FUNCTION "dbo"."CalculateOrderTotal"(order_id INTEGER)
RETURNS NUMERIC AS $$
DECLARE
    total NUMERIC(10,2);
BEGIN
    SELECT COALESCE(SUM("Price" * "Quantity"), 0)
    INTO total
    FROM "dbo"."OrderItems" oi
    JOIN "dbo"."Products" p ON oi."ProductID" = p."ProductID"
    WHERE oi."OrderID" = order_id;
    
    RETURN total;
END;
$$ LANGUAGE plpgsql;

-- Procedure: dbo.UpdateUserStatus
CREATE SCHEMA IF NOT EXISTS "dbo";

DROP PROCEDURE IF EXISTS "dbo"."UpdateUserStatus" CASCADE;

CREATE OR REPLACE FUNCTION "dbo"."UpdateUserStatus"(user_id INTEGER, new_status BOOLEAN)
RETURNS VOID AS $$
BEGIN
    UPDATE "dbo"."Users"
    SET "IsActive" = new_status,
        "ModifiedDate" = CURRENT_TIMESTAMP
    WHERE "UserID" = user_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'User with ID % not found', user_id;
    END IF;
    
    RAISE NOTICE 'User % status updated to %', user_id, new_status;
END;
$$ LANGUAGE plpgsql;

-- Procedure: dbo.CleanupOldOrders
CREATE SCHEMA IF NOT EXISTS "dbo";

DROP PROCEDURE IF EXISTS "dbo"."CleanupOldOrders" CASCADE;

CREATE OR REPLACE FUNCTION "dbo"."CleanupOldOrders"(days_old INTEGER DEFAULT 365)
RETURNS VOID AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM "dbo"."Orders"
    WHERE "OrderDate" < CURRENT_DATE - INTERVAL '1 day' * days_old
    AND "Status" IN ('Completed', 'Cancelled');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RAISE NOTICE 'Deleted % old orders', deleted_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- Migration completed
-- =====================================================

-- Commit transaction
COMMIT;

-- Analyze tables for better performance
ANALYZE;
