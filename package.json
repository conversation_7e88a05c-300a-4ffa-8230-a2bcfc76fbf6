{"name": "mssql2postgresql-migrator", "version": "1.0.0", "description": "Desktop application for migrating databases from MSSQL to PostgreSQL", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "test": "node tests/test-connections.js", "test-jest": "jest", "lint": "eslint src/", "clean": "rimraf dist build logs/*.log", "postinstall": "electron-builder install-app-deps"}, "keywords": ["database", "migration", "mssql", "postgresql", "electron", "desktop"], "author": "Your Name", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1", "eslint": "^8.56.0", "jest": "^29.7.0"}, "dependencies": {"bootstrap": "^5.3.2", "bootstrap-icons": "^1.11.2", "jquery": "^3.7.1", "lodash": "^4.17.21", "moment": "^2.29.4", "mssql": "^10.0.1", "pg": "^8.11.3", "winston": "^3.11.0"}, "build": {"appId": "com.yourcompany.mssql2postgresql", "productName": "MSSQL to PostgreSQL Migrator", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}