const logger = require('../utils/logger');

class MigrationScriptGenerator {
    constructor() {
        this.dataTypeMapping = this.initializeDataTypeMapping();
    }

    /**
     * Generate complete migration script
     * @param {Array} objects - Selected objects to migrate
     * @param {Object} mssqlConnection - MSSQL connection instance
     * @returns {Promise<string>} - Generated migration script
     */
    async generateMigrationScript(objects, mssqlConnection) {
        try {
            logger.migration.start('Migration script generation');
            
            let script = this.generateScriptHeader();
            
            // Sort objects by dependency order (tables first, then views, procedures, functions)
            const sortedObjects = this.sortObjectsByDependency(objects);
            
            for (const obj of sortedObjects) {
                logger.migration.progress(`Generating script for ${obj.type}: ${obj.schema}.${obj.name}`);
                
                try {
                    const objectScript = await this.generateObjectScript(obj, mssqlConnection);
                    script += objectScript + '\n\n';
                } catch (error) {
                    logger.migration.warning(`Failed to generate script for ${obj.schema}.${obj.name}: ${error.message}`);
                    script += `-- ERROR: Failed to generate script for ${obj.schema}.${obj.name}\n`;
                    script += `-- ${error.message}\n\n`;
                }
            }
            
            script += this.generateScriptFooter();
            
            logger.migration.success(`Generated migration script for ${objects.length} objects`);
            return script;
            
        } catch (error) {
            logger.migration.error('Migration script generation', error);
            throw error;
        }
    }

    /**
     * Generate script for a single object
     * @param {Object} obj - Database object
     * @param {Object} mssqlConnection - MSSQL connection instance
     * @returns {Promise<string>} - Object script
     */
    async generateObjectScript(obj, mssqlConnection) {
        switch (obj.type) {
            case 'table':
                return await this.generateTableScript(obj, mssqlConnection);
            case 'view':
                return await this.generateViewScript(obj, mssqlConnection);
            case 'procedure':
                return await this.generateProcedureScript(obj, mssqlConnection);
            case 'function':
                return await this.generateFunctionScript(obj, mssqlConnection);
            default:
                throw new Error(`Unsupported object type: ${obj.type}`);
        }
    }

    /**
     * Generate table creation script
     * @param {Object} table - Table object
     * @param {Object} mssqlConnection - MSSQL connection instance
     * @returns {Promise<string>} - Table script
     */
    async generateTableScript(table, mssqlConnection) {
        const structure = await mssqlConnection.getTableStructure(table.schema, table.name);
        
        let script = `-- Table: ${table.schema}.${table.name}\n`;
        script += `CREATE SCHEMA IF NOT EXISTS "${table.schema}";\n\n`;
        script += `DROP TABLE IF EXISTS "${table.schema}"."${table.name}" CASCADE;\n\n`;
        script += `CREATE TABLE "${table.schema}"."${table.name}" (\n`;
        
        const columns = [];
        const primaryKeys = [];
        const foreignKeys = [];
        
        for (const col of structure) {
            // Column definition
            const columnDef = this.generateColumnDefinition(col);
            columns.push(`    "${col.COLUMN_NAME}" ${columnDef}`);
            
            // Primary key
            if (col.IS_PRIMARY_KEY) {
                primaryKeys.push(`"${col.COLUMN_NAME}"`);
            }
            
            // Foreign key
            if (col.IS_FOREIGN_KEY) {
                foreignKeys.push({
                    column: col.COLUMN_NAME,
                    refSchema: col.REFERENCED_TABLE_SCHEMA,
                    refTable: col.REFERENCED_TABLE_NAME,
                    refColumn: col.REFERENCED_COLUMN_NAME
                });
            }
        }
        
        script += columns.join(',\n');
        
        // Add primary key constraint
        if (primaryKeys.length > 0) {
            script += `,\n    CONSTRAINT "pk_${table.name}" PRIMARY KEY (${primaryKeys.join(', ')})`;
        }
        
        script += '\n);\n\n';
        
        // Add foreign key constraints
        for (const fk of foreignKeys) {
            script += `ALTER TABLE "${table.schema}"."${table.name}" `;
            script += `ADD CONSTRAINT "fk_${table.name}_${fk.column}" `;
            script += `FOREIGN KEY ("${fk.column}") `;
            script += `REFERENCES "${fk.refSchema}"."${fk.refTable}" ("${fk.refColumn}");\n\n`;
        }
        
        // Add indexes (basic implementation)
        script += await this.generateIndexes(table, mssqlConnection);
        
        return script;
    }

    /**
     * Generate column definition
     * @param {Object} column - Column information
     * @returns {string} - Column definition
     */
    generateColumnDefinition(column) {
        let dataType = this.mapDataType(column.DATA_TYPE, column);
        let definition = dataType;
        
        // Handle nullable
        if (column.IS_NULLABLE === 'NO') {
            definition += ' NOT NULL';
        }
        
        // Handle default values
        if (column.COLUMN_DEFAULT) {
            let defaultValue = this.mapDefaultValue(column.COLUMN_DEFAULT, column.DATA_TYPE);
            if (defaultValue) {
                definition += ` DEFAULT ${defaultValue}`;
            }
        }
        
        return definition;
    }

    /**
     * Map MSSQL data types to PostgreSQL
     * @param {string} mssqlType - MSSQL data type
     * @param {Object} column - Column information
     * @returns {string} - PostgreSQL data type
     */
    mapDataType(mssqlType, column) {
        const type = mssqlType.toLowerCase();
        
        // Handle specific mappings with length/precision
        switch (type) {
            case 'varchar':
                return column.CHARACTER_MAXIMUM_LENGTH 
                    ? `VARCHAR(${column.CHARACTER_MAXIMUM_LENGTH})` 
                    : 'TEXT';
            
            case 'nvarchar':
                return column.CHARACTER_MAXIMUM_LENGTH 
                    ? `VARCHAR(${column.CHARACTER_MAXIMUM_LENGTH})` 
                    : 'TEXT';
            
            case 'char':
                return column.CHARACTER_MAXIMUM_LENGTH 
                    ? `CHAR(${column.CHARACTER_MAXIMUM_LENGTH})` 
                    : 'CHAR(1)';
            
            case 'nchar':
                return column.CHARACTER_MAXIMUM_LENGTH 
                    ? `CHAR(${column.CHARACTER_MAXIMUM_LENGTH})` 
                    : 'CHAR(1)';
            
            case 'decimal':
            case 'numeric':
                if (column.NUMERIC_PRECISION && column.NUMERIC_SCALE !== null) {
                    return `NUMERIC(${column.NUMERIC_PRECISION},${column.NUMERIC_SCALE})`;
                }
                return 'NUMERIC';
            
            case 'float':
                return column.NUMERIC_PRECISION 
                    ? `REAL` 
                    : 'DOUBLE PRECISION';
            
            default:
                return this.dataTypeMapping[type] || 'TEXT';
        }
    }

    /**
     * Initialize data type mapping
     * @returns {Object} - Data type mapping
     */
    initializeDataTypeMapping() {
        return {
            // String types
            'text': 'TEXT',
            'ntext': 'TEXT',
            'varchar': 'VARCHAR',
            'nvarchar': 'VARCHAR',
            'char': 'CHAR',
            'nchar': 'CHAR',
            
            // Numeric types
            'int': 'INTEGER',
            'integer': 'INTEGER',
            'bigint': 'BIGINT',
            'smallint': 'SMALLINT',
            'tinyint': 'SMALLINT',
            'bit': 'BOOLEAN',
            'decimal': 'NUMERIC',
            'numeric': 'NUMERIC',
            'money': 'MONEY',
            'smallmoney': 'MONEY',
            'float': 'DOUBLE PRECISION',
            'real': 'REAL',
            
            // Date/Time types
            'datetime': 'TIMESTAMP',
            'datetime2': 'TIMESTAMP',
            'smalldatetime': 'TIMESTAMP',
            'date': 'DATE',
            'time': 'TIME',
            'datetimeoffset': 'TIMESTAMP WITH TIME ZONE',
            
            // Binary types
            'binary': 'BYTEA',
            'varbinary': 'BYTEA',
            'image': 'BYTEA',
            
            // Other types
            'uniqueidentifier': 'UUID',
            'xml': 'XML',
            'sql_variant': 'TEXT',
            'timestamp': 'BYTEA',
            'rowversion': 'BYTEA'
        };
    }

    /**
     * Map default values
     * @param {string} mssqlDefault - MSSQL default value
     * @param {string} dataType - Data type
     * @returns {string} - PostgreSQL default value
     */
    mapDefaultValue(mssqlDefault, dataType) {
        if (!mssqlDefault) return null;
        
        let defaultValue = mssqlDefault.trim();
        
        // Remove parentheses if present
        if (defaultValue.startsWith('(') && defaultValue.endsWith(')')) {
            defaultValue = defaultValue.slice(1, -1);
        }
        
        // Handle common MSSQL functions
        const functionMappings = {
            'getdate()': 'CURRENT_TIMESTAMP',
            'getutcdate()': 'CURRENT_TIMESTAMP',
            'newid()': 'gen_random_uuid()',
            'user_name()': 'CURRENT_USER',
            'suser_name()': 'CURRENT_USER'
        };
        
        const lowerDefault = defaultValue.toLowerCase();
        if (functionMappings[lowerDefault]) {
            return functionMappings[lowerDefault];
        }
        
        // Handle string literals
        if (defaultValue.startsWith("'") && defaultValue.endsWith("'")) {
            return defaultValue;
        }
        
        // Handle numeric values
        if (!isNaN(defaultValue)) {
            return defaultValue;
        }
        
        // Handle boolean values
        if (lowerDefault === '1' || lowerDefault === 'true') {
            return 'true';
        }
        if (lowerDefault === '0' || lowerDefault === 'false') {
            return 'false';
        }
        
        // Return as-is for other cases
        return `'${defaultValue}'`;
    }

    /**
     * Generate indexes for table
     * @param {Object} table - Table object
     * @param {Object} mssqlConnection - MSSQL connection instance
     * @returns {Promise<string>} - Index scripts
     */
    async generateIndexes(table, mssqlConnection) {
        try {
            const query = `
                SELECT
                    i.name AS index_name,
                    i.is_unique,
                    STRING_AGG(c.name, ', ') AS columns
                FROM sys.indexes i
                INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
                INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
                WHERE i.object_id = OBJECT_ID('${table.schema}.${table.name}')
                AND i.type > 0  -- Exclude heaps
                AND i.is_primary_key = 0  -- Exclude primary key
                GROUP BY i.name, i.is_unique
            `;

            const result = await mssqlConnection.executeQuery(query);
            let script = '';

            for (const index of result.recordset) {
                const uniqueKeyword = index.is_unique ? 'UNIQUE ' : '';
                script += `CREATE ${uniqueKeyword}INDEX "${index.index_name}" `;
                script += `ON "${table.schema}"."${table.name}" (${index.columns});\n`;
            }

            return script ? script + '\n' : '';

        } catch (error) {
            logger.warning(`Failed to generate indexes for ${table.schema}.${table.name}: ${error.message}`);
            return '';
        }
    }

    /**
     * Generate view creation script
     * @param {Object} view - View object
     * @param {Object} mssqlConnection - MSSQL connection instance
     * @returns {Promise<string>} - View script
     */
    async generateViewScript(view, mssqlConnection) {
        const definition = await mssqlConnection.getViewDefinition(view.schema, view.name);

        let script = `-- View: ${view.schema}.${view.name}\n`;
        script += `CREATE SCHEMA IF NOT EXISTS "${view.schema}";\n\n`;
        script += `DROP VIEW IF EXISTS "${view.schema}"."${view.name}" CASCADE;\n\n`;

        // Convert MSSQL view definition to PostgreSQL
        const convertedDefinition = this.convertViewDefinition(definition);

        script += `CREATE VIEW "${view.schema}"."${view.name}" AS\n`;
        script += convertedDefinition;
        script += ';\n';

        return script;
    }

    /**
     * Generate procedure creation script
     * @param {Object} procedure - Procedure object
     * @param {Object} mssqlConnection - MSSQL connection instance
     * @returns {Promise<string>} - Procedure script
     */
    async generateProcedureScript(procedure, mssqlConnection) {
        const definition = await mssqlConnection.getProcedureDefinition(procedure.schema, procedure.name);

        let script = `-- Procedure: ${procedure.schema}.${procedure.name}\n`;
        script += `CREATE SCHEMA IF NOT EXISTS "${procedure.schema}";\n\n`;
        script += `DROP PROCEDURE IF EXISTS "${procedure.schema}"."${procedure.name}" CASCADE;\n\n`;

        // Convert MSSQL procedure to PostgreSQL function
        const convertedDefinition = this.convertProcedureDefinition(definition, procedure);

        script += convertedDefinition;
        script += ';\n';

        return script;
    }

    /**
     * Generate function creation script
     * @param {Object} func - Function object
     * @param {Object} mssqlConnection - MSSQL connection instance
     * @returns {Promise<string>} - Function script
     */
    async generateFunctionScript(func, mssqlConnection) {
        const definition = await mssqlConnection.getProcedureDefinition(func.schema, func.name);

        let script = `-- Function: ${func.schema}.${func.name}\n`;
        script += `CREATE SCHEMA IF NOT EXISTS "${func.schema}";\n\n`;
        script += `DROP FUNCTION IF EXISTS "${func.schema}"."${func.name}" CASCADE;\n\n`;

        // Convert MSSQL function to PostgreSQL function
        const convertedDefinition = this.convertFunctionDefinition(definition, func);

        script += convertedDefinition;
        script += ';\n';

        return script;
    }

    /**
     * Convert MSSQL view definition to PostgreSQL
     * @param {string} definition - MSSQL view definition
     * @returns {string} - PostgreSQL view definition
     */
    convertViewDefinition(definition) {
        if (!definition) return 'SELECT 1 as placeholder';

        let converted = definition;

        // Basic conversions
        converted = this.convertCommonSqlSyntax(converted);

        // Remove CREATE VIEW statement if present
        converted = converted.replace(/CREATE\s+VIEW\s+.*?\s+AS\s*/i, '');

        return converted.trim();
    }

    /**
     * Convert MSSQL procedure to PostgreSQL function
     * @param {string} definition - MSSQL procedure definition
     * @param {Object} procedure - Procedure object
     * @returns {string} - PostgreSQL function definition
     */
    convertProcedureDefinition(definition, procedure) {
        if (!definition) {
            return `CREATE OR REPLACE FUNCTION "${procedure.schema}"."${procedure.name}"()
RETURNS VOID AS $$
BEGIN
    -- Procedure body could not be retrieved
    -- Please implement manually
    RAISE NOTICE 'Procedure ${procedure.name} needs manual implementation';
END;
$$ LANGUAGE plpgsql`;
        }

        let converted = definition;

        // Convert procedure to function
        converted = converted.replace(/CREATE\s+PROCEDURE/i, 'CREATE OR REPLACE FUNCTION');
        converted = converted.replace(/AS\s*$/i, 'RETURNS VOID AS $$');
        converted += '\n$$ LANGUAGE plpgsql';

        // Basic conversions
        converted = this.convertCommonSqlSyntax(converted);

        return converted;
    }

    /**
     * Convert MSSQL function to PostgreSQL function
     * @param {string} definition - MSSQL function definition
     * @param {Object} func - Function object
     * @returns {string} - PostgreSQL function definition
     */
    convertFunctionDefinition(definition, func) {
        if (!definition) {
            return `CREATE OR REPLACE FUNCTION "${func.schema}"."${func.name}"()
RETURNS TEXT AS $$
BEGIN
    -- Function body could not be retrieved
    -- Please implement manually
    RETURN 'Function ${func.name} needs manual implementation';
END;
$$ LANGUAGE plpgsql`;
        }

        let converted = definition;

        // Convert function syntax
        converted = converted.replace(/CREATE\s+FUNCTION/i, 'CREATE OR REPLACE FUNCTION');
        converted = converted.replace(/RETURNS\s+.*?\s+AS/i, 'RETURNS TEXT AS $$');
        converted += '\n$$ LANGUAGE plpgsql';

        // Basic conversions
        converted = this.convertCommonSqlSyntax(converted);

        return converted;
    }

    /**
     * Convert common SQL syntax from MSSQL to PostgreSQL
     * @param {string} sql - MSSQL SQL
     * @returns {string} - PostgreSQL SQL
     */
    convertCommonSqlSyntax(sql) {
        if (!sql) return sql;

        let converted = sql;

        // Convert square brackets to double quotes
        converted = converted.replace(/\[([^\]]+)\]/g, '"$1"');

        // Convert ISNULL to COALESCE
        converted = converted.replace(/ISNULL\s*\(/gi, 'COALESCE(');

        // Convert LEN to LENGTH
        converted = converted.replace(/\bLEN\s*\(/gi, 'LENGTH(');

        // Convert CHARINDEX to POSITION
        converted = converted.replace(/CHARINDEX\s*\(\s*'([^']+)'\s*,\s*([^)]+)\)/gi, "POSITION('$1' IN $2)");

        // Convert GETDATE() to CURRENT_TIMESTAMP
        converted = converted.replace(/GETDATE\s*\(\s*\)/gi, 'CURRENT_TIMESTAMP');

        // Convert NEWID() to gen_random_uuid()
        converted = converted.replace(/NEWID\s*\(\s*\)/gi, 'gen_random_uuid()');

        // Convert TOP to LIMIT
        converted = converted.replace(/SELECT\s+TOP\s+(\d+)/gi, 'SELECT');
        const topMatch = sql.match(/SELECT\s+TOP\s+(\d+)/i);
        if (topMatch) {
            converted += ` LIMIT ${topMatch[1]}`;
        }

        // Convert DATEPART to EXTRACT
        converted = converted.replace(/DATEPART\s*\(\s*(\w+)\s*,\s*([^)]+)\)/gi, 'EXTRACT($1 FROM $2)');

        // Convert DATEDIFF (basic conversion)
        converted = converted.replace(/DATEDIFF\s*\(\s*day\s*,\s*([^,]+)\s*,\s*([^)]+)\)/gi, '($2::date - $1::date)');

        return converted;
    }

    /**
     * Sort objects by dependency order
     * @param {Array} objects - Objects to sort
     * @returns {Array} - Sorted objects
     */
    sortObjectsByDependency(objects) {
        const typeOrder = {
            'table': 1,
            'view': 2,
            'function': 3,
            'procedure': 4
        };

        return objects.sort((a, b) => {
            const orderA = typeOrder[a.type] || 999;
            const orderB = typeOrder[b.type] || 999;

            if (orderA !== orderB) {
                return orderA - orderB;
            }

            // Same type, sort by schema then name
            if (a.schema !== b.schema) {
                return a.schema.localeCompare(b.schema);
            }

            return a.name.localeCompare(b.name);
        });
    }

    /**
     * Generate script header
     * @returns {string} - Script header
     */
    generateScriptHeader() {
        const timestamp = new Date().toISOString();
        return `-- =====================================================
-- MSSQL to PostgreSQL Migration Script
-- Generated on: ${timestamp}
-- =====================================================

-- Enable UUID extension if not exists
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Set client encoding
SET client_encoding = 'UTF8';

-- Begin transaction
BEGIN;

`;
    }

    /**
     * Generate script footer
     * @returns {string} - Script footer
     */
    generateScriptFooter() {
        return `
-- =====================================================
-- Migration completed
-- =====================================================

-- Commit transaction
COMMIT;

-- Analyze tables for better performance
ANALYZE;
`;
    }
}

module.exports = MigrationScriptGenerator;
