const sql = require('mssql');
const logger = require('../utils/logger');

class MSSQLConnection {
    constructor() {
        this.pool = null;
        this.config = null;
    }

    /**
     * Test database connection
     * @param {Object} config - Database configuration
     * @returns {Promise<boolean>} - Connection test result
     */
    async testConnection(config) {
        try {
            const testConfig = {
                server: config.server,
                database: config.database,
                user: config.username,
                password: config.password,
                options: {
                    encrypt: false,
                    trustServerCertificate: true,
                    enableArithAbort: true
                },
                connectionTimeout: 10000,
                requestTimeout: 10000
            };

            const testPool = new sql.ConnectionPool(testConfig);
            await testPool.connect();
            await testPool.close();
            
            logger.info('MSSQL connection test successful');
            return true;
        } catch (error) {
            logger.error('MSSQL connection test failed:', error.message);
            throw new Error(`Connection failed: ${error.message}`);
        }
    }

    /**
     * Connect to database
     * @param {Object} config - Database configuration
     */
    async connect(config) {
        try {
            if (this.pool) {
                await this.disconnect();
            }

            this.config = {
                server: config.server,
                database: config.database,
                user: config.username,
                password: config.password,
                options: {
                    encrypt: false,
                    trustServerCertificate: true,
                    enableArithAbort: true
                },
                connectionTimeout: 30000,
                requestTimeout: 30000,
                pool: {
                    max: 10,
                    min: 0,
                    idleTimeoutMillis: 30000
                }
            };

            this.pool = new sql.ConnectionPool(this.config);
            await this.pool.connect();
            
            logger.info('Connected to MSSQL database');
        } catch (error) {
            logger.error('Failed to connect to MSSQL:', error.message);
            throw error;
        }
    }

    /**
     * Disconnect from database
     */
    async disconnect() {
        try {
            if (this.pool) {
                await this.pool.close();
                this.pool = null;
                logger.info('Disconnected from MSSQL database');
            }
        } catch (error) {
            logger.error('Error disconnecting from MSSQL:', error.message);
        }
    }

    /**
     * Execute query
     * @param {string} query - SQL query
     * @returns {Promise<Object>} - Query result
     */
    async executeQuery(query) {
        try {
            if (!this.pool) {
                throw new Error('Not connected to database');
            }

            const request = this.pool.request();
            const result = await request.query(query);
            return result;
        } catch (error) {
            logger.error('Query execution failed:', error.message);
            throw error;
        }
    }

    /**
     * Get all database objects
     * @returns {Promise<Array>} - List of database objects
     */
    async getDatabaseObjects() {
        const query = `
            SELECT 
                SCHEMA_NAME(schema_id) as schema_name,
                name as object_name,
                type_desc as object_type,
                create_date,
                modify_date
            FROM sys.objects 
            WHERE type IN ('U', 'V', 'P', 'FN', 'IF', 'TF')
            AND is_ms_shipped = 0
            ORDER BY schema_name, type_desc, name
        `;

        try {
            const result = await this.executeQuery(query);
            return result.recordset.map(row => ({
                schema: row.schema_name,
                name: row.object_name,
                type: this.mapObjectType(row.object_type),
                created: row.create_date,
                modified: row.modify_date
            }));
        } catch (error) {
            logger.error('Failed to get database objects:', error.message);
            throw error;
        }
    }

    /**
     * Get table structure
     * @param {string} schema - Schema name
     * @param {string} tableName - Table name
     * @returns {Promise<Object>} - Table structure
     */
    async getTableStructure(schema, tableName) {
        const query = `
            SELECT 
                c.COLUMN_NAME,
                c.DATA_TYPE,
                c.CHARACTER_MAXIMUM_LENGTH,
                c.NUMERIC_PRECISION,
                c.NUMERIC_SCALE,
                c.IS_NULLABLE,
                c.COLUMN_DEFAULT,
                CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 1 ELSE 0 END as IS_PRIMARY_KEY,
                CASE WHEN fk.COLUMN_NAME IS NOT NULL THEN 1 ELSE 0 END as IS_FOREIGN_KEY,
                fk.REFERENCED_TABLE_SCHEMA,
                fk.REFERENCED_TABLE_NAME,
                fk.REFERENCED_COLUMN_NAME
            FROM INFORMATION_SCHEMA.COLUMNS c
            LEFT JOIN (
                SELECT ku.TABLE_SCHEMA, ku.TABLE_NAME, ku.COLUMN_NAME
                FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
                INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku
                    ON tc.CONSTRAINT_TYPE = 'PRIMARY KEY' 
                    AND tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
            ) pk ON c.TABLE_SCHEMA = pk.TABLE_SCHEMA 
                AND c.TABLE_NAME = pk.TABLE_NAME 
                AND c.COLUMN_NAME = pk.COLUMN_NAME
            LEFT JOIN (
                SELECT 
                    ku.TABLE_SCHEMA, ku.TABLE_NAME, ku.COLUMN_NAME,
                    ku.REFERENCED_TABLE_SCHEMA, ku.REFERENCED_TABLE_NAME, ku.REFERENCED_COLUMN_NAME
                FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
                INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku
                    ON rc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
            ) fk ON c.TABLE_SCHEMA = fk.TABLE_SCHEMA 
                AND c.TABLE_NAME = fk.TABLE_NAME 
                AND c.COLUMN_NAME = fk.COLUMN_NAME
            WHERE c.TABLE_SCHEMA = '${schema}' AND c.TABLE_NAME = '${tableName}'
            ORDER BY c.ORDINAL_POSITION
        `;

        try {
            const result = await this.executeQuery(query);
            return result.recordset;
        } catch (error) {
            logger.error('Failed to get table structure:', error.message);
            throw error;
        }
    }

    /**
     * Get view definition
     * @param {string} schema - Schema name
     * @param {string} viewName - View name
     * @returns {Promise<string>} - View definition
     */
    async getViewDefinition(schema, viewName) {
        const query = `
            SELECT VIEW_DEFINITION 
            FROM INFORMATION_SCHEMA.VIEWS 
            WHERE TABLE_SCHEMA = '${schema}' AND TABLE_NAME = '${viewName}'
        `;

        try {
            const result = await this.executeQuery(query);
            return result.recordset[0]?.VIEW_DEFINITION || '';
        } catch (error) {
            logger.error('Failed to get view definition:', error.message);
            throw error;
        }
    }

    /**
     * Get stored procedure definition
     * @param {string} schema - Schema name
     * @param {string} procedureName - Procedure name
     * @returns {Promise<string>} - Procedure definition
     */
    async getProcedureDefinition(schema, procedureName) {
        const query = `
            SELECT ROUTINE_DEFINITION 
            FROM INFORMATION_SCHEMA.ROUTINES 
            WHERE ROUTINE_SCHEMA = '${schema}' 
            AND ROUTINE_NAME = '${procedureName}'
            AND ROUTINE_TYPE = 'PROCEDURE'
        `;

        try {
            const result = await this.executeQuery(query);
            return result.recordset[0]?.ROUTINE_DEFINITION || '';
        } catch (error) {
            logger.error('Failed to get procedure definition:', error.message);
            throw error;
        }
    }

    /**
     * Map MSSQL object types to standard types
     * @param {string} mssqlType - MSSQL object type
     * @returns {string} - Standard object type
     */
    mapObjectType(mssqlType) {
        const typeMap = {
            'USER_TABLE': 'table',
            'VIEW': 'view',
            'SQL_STORED_PROCEDURE': 'procedure',
            'SQL_SCALAR_FUNCTION': 'function',
            'SQL_INLINE_TABLE_VALUED_FUNCTION': 'function',
            'SQL_TABLE_VALUED_FUNCTION': 'function'
        };

        return typeMap[mssqlType] || 'unknown';
    }

    /**
     * Check if connected
     * @returns {boolean} - Connection status
     */
    isConnected() {
        return this.pool && this.pool.connected;
    }
}

module.exports = MSSQLConnection;
