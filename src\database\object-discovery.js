const logger = require('../utils/logger');

class ObjectDiscovery {
    constructor(mssqlConnection) {
        this.mssqlConnection = mssqlConnection;
    }

    /**
     * Discover all database objects
     * @returns {Promise<Array>} - List of discovered objects
     */
    async discoverAllObjects() {
        try {
            logger.migration.start('Database object discovery');
            
            if (!this.mssqlConnection.isConnected()) {
                throw new Error('MSSQL connection is not established');
            }

            const objects = await this.mssqlConnection.getDatabaseObjects();
            
            // Enhance objects with additional metadata
            const enhancedObjects = await Promise.all(
                objects.map(async (obj) => {
                    try {
                        const metadata = await this.getObjectMetadata(obj);
                        return {
                            ...obj,
                            ...metadata,
                            selected: false,
                            status: 'ready'
                        };
                    } catch (error) {
                        logger.warning(`Failed to get metadata for ${obj.schema}.${obj.name}: ${error.message}`);
                        return {
                            ...obj,
                            selected: false,
                            status: 'error',
                            error: error.message
                        };
                    }
                })
            );

            logger.migration.success(`Discovered ${enhancedObjects.length} database objects`);
            return enhancedObjects;
        } catch (error) {
            logger.migration.error('Database object discovery', error);
            throw error;
        }
    }

    /**
     * Get metadata for a specific object
     * @param {Object} obj - Database object
     * @returns {Promise<Object>} - Object metadata
     */
    async getObjectMetadata(obj) {
        const metadata = {
            rowCount: null,
            size: null,
            dependencies: [],
            columns: null
        };

        try {
            switch (obj.type) {
                case 'table':
                    metadata.rowCount = await this.getTableRowCount(obj.schema, obj.name);
                    metadata.columns = await this.getTableColumns(obj.schema, obj.name);
                    metadata.dependencies = await this.getTableDependencies(obj.schema, obj.name);
                    break;
                
                case 'view':
                    metadata.columns = await this.getViewColumns(obj.schema, obj.name);
                    metadata.dependencies = await this.getViewDependencies(obj.schema, obj.name);
                    break;
                
                case 'procedure':
                    metadata.parameters = await this.getProcedureParameters(obj.schema, obj.name);
                    break;
                
                case 'function':
                    metadata.parameters = await this.getFunctionParameters(obj.schema, obj.name);
                    metadata.returnType = await this.getFunctionReturnType(obj.schema, obj.name);
                    break;
            }
        } catch (error) {
            logger.warning(`Failed to get complete metadata for ${obj.schema}.${obj.name}: ${error.message}`);
        }

        return metadata;
    }

    /**
     * Get table row count
     * @param {string} schema - Schema name
     * @param {string} tableName - Table name
     * @returns {Promise<number>} - Row count
     */
    async getTableRowCount(schema, tableName) {
        try {
            const query = `SELECT COUNT(*) as row_count FROM [${schema}].[${tableName}]`;
            const result = await this.mssqlConnection.executeQuery(query);
            return result.recordset[0].row_count;
        } catch (error) {
            logger.warning(`Failed to get row count for ${schema}.${tableName}: ${error.message}`);
            return null;
        }
    }

    /**
     * Get table columns
     * @param {string} schema - Schema name
     * @param {string} tableName - Table name
     * @returns {Promise<Array>} - Column information
     */
    async getTableColumns(schema, tableName) {
        try {
            return await this.mssqlConnection.getTableStructure(schema, tableName);
        } catch (error) {
            logger.warning(`Failed to get columns for ${schema}.${tableName}: ${error.message}`);
            return [];
        }
    }

    /**
     * Get view columns
     * @param {string} schema - Schema name
     * @param {string} viewName - View name
     * @returns {Promise<Array>} - Column information
     */
    async getViewColumns(schema, viewName) {
        try {
            const query = `
                SELECT 
                    COLUMN_NAME,
                    DATA_TYPE,
                    CHARACTER_MAXIMUM_LENGTH,
                    NUMERIC_PRECISION,
                    NUMERIC_SCALE,
                    IS_NULLABLE
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = '${schema}' AND TABLE_NAME = '${viewName}'
                ORDER BY ORDINAL_POSITION
            `;
            
            const result = await this.mssqlConnection.executeQuery(query);
            return result.recordset;
        } catch (error) {
            logger.warning(`Failed to get columns for view ${schema}.${viewName}: ${error.message}`);
            return [];
        }
    }

    /**
     * Get table dependencies (foreign keys)
     * @param {string} schema - Schema name
     * @param {string} tableName - Table name
     * @returns {Promise<Array>} - Dependencies
     */
    async getTableDependencies(schema, tableName) {
        try {
            const query = `
                SELECT 
                    fk.name AS foreign_key_name,
                    OBJECT_SCHEMA_NAME(fk.parent_object_id) AS schema_name,
                    OBJECT_NAME(fk.parent_object_id) AS table_name,
                    COL_NAME(fkc.parent_object_id, fkc.parent_column_id) AS column_name,
                    OBJECT_SCHEMA_NAME(fk.referenced_object_id) AS referenced_schema,
                    OBJECT_NAME(fk.referenced_object_id) AS referenced_table,
                    COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) AS referenced_column
                FROM sys.foreign_keys fk
                INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
                WHERE OBJECT_SCHEMA_NAME(fk.parent_object_id) = '${schema}'
                AND OBJECT_NAME(fk.parent_object_id) = '${tableName}'
            `;
            
            const result = await this.mssqlConnection.executeQuery(query);
            return result.recordset;
        } catch (error) {
            logger.warning(`Failed to get dependencies for ${schema}.${tableName}: ${error.message}`);
            return [];
        }
    }

    /**
     * Get view dependencies
     * @param {string} schema - Schema name
     * @param {string} viewName - View name
     * @returns {Promise<Array>} - Dependencies
     */
    async getViewDependencies(schema, viewName) {
        try {
            const query = `
                SELECT 
                    OBJECT_SCHEMA_NAME(sed.referenced_major_id) AS referenced_schema,
                    OBJECT_NAME(sed.referenced_major_id) AS referenced_object,
                    o.type_desc AS referenced_type
                FROM sys.sql_expression_dependencies sed
                INNER JOIN sys.objects o ON sed.referenced_major_id = o.object_id
                WHERE OBJECT_SCHEMA_NAME(sed.referencing_id) = '${schema}'
                AND OBJECT_NAME(sed.referencing_id) = '${viewName}'
            `;
            
            const result = await this.mssqlConnection.executeQuery(query);
            return result.recordset;
        } catch (error) {
            logger.warning(`Failed to get dependencies for view ${schema}.${viewName}: ${error.message}`);
            return [];
        }
    }

    /**
     * Get procedure parameters
     * @param {string} schema - Schema name
     * @param {string} procedureName - Procedure name
     * @returns {Promise<Array>} - Parameters
     */
    async getProcedureParameters(schema, procedureName) {
        try {
            const query = `
                SELECT 
                    PARAMETER_NAME,
                    DATA_TYPE,
                    CHARACTER_MAXIMUM_LENGTH,
                    NUMERIC_PRECISION,
                    NUMERIC_SCALE,
                    PARAMETER_MODE
                FROM INFORMATION_SCHEMA.PARAMETERS
                WHERE SPECIFIC_SCHEMA = '${schema}' 
                AND SPECIFIC_NAME = '${procedureName}'
                ORDER BY ORDINAL_POSITION
            `;
            
            const result = await this.mssqlConnection.executeQuery(query);
            return result.recordset;
        } catch (error) {
            logger.warning(`Failed to get parameters for procedure ${schema}.${procedureName}: ${error.message}`);
            return [];
        }
    }

    /**
     * Get function parameters
     * @param {string} schema - Schema name
     * @param {string} functionName - Function name
     * @returns {Promise<Array>} - Parameters
     */
    async getFunctionParameters(schema, functionName) {
        try {
            const query = `
                SELECT 
                    PARAMETER_NAME,
                    DATA_TYPE,
                    CHARACTER_MAXIMUM_LENGTH,
                    NUMERIC_PRECISION,
                    NUMERIC_SCALE,
                    PARAMETER_MODE
                FROM INFORMATION_SCHEMA.PARAMETERS
                WHERE SPECIFIC_SCHEMA = '${schema}' 
                AND SPECIFIC_NAME = '${functionName}'
                AND PARAMETER_NAME IS NOT NULL
                ORDER BY ORDINAL_POSITION
            `;
            
            const result = await this.mssqlConnection.executeQuery(query);
            return result.recordset;
        } catch (error) {
            logger.warning(`Failed to get parameters for function ${schema}.${functionName}: ${error.message}`);
            return [];
        }
    }

    /**
     * Get function return type
     * @param {string} schema - Schema name
     * @param {string} functionName - Function name
     * @returns {Promise<string>} - Return type
     */
    async getFunctionReturnType(schema, functionName) {
        try {
            const query = `
                SELECT DATA_TYPE
                FROM INFORMATION_SCHEMA.ROUTINES
                WHERE ROUTINE_SCHEMA = '${schema}' 
                AND ROUTINE_NAME = '${functionName}'
                AND ROUTINE_TYPE = 'FUNCTION'
            `;
            
            const result = await this.mssqlConnection.executeQuery(query);
            return result.recordset[0]?.DATA_TYPE || 'unknown';
        } catch (error) {
            logger.warning(`Failed to get return type for function ${schema}.${functionName}: ${error.message}`);
            return 'unknown';
        }
    }

    /**
     * Search objects by name or type
     * @param {Array} objects - List of objects to search
     * @param {string} searchTerm - Search term
     * @param {string} objectType - Object type filter
     * @returns {Array} - Filtered objects
     */
    searchObjects(objects, searchTerm = '', objectType = '') {
        let filtered = objects;

        // Filter by object type
        if (objectType) {
            filtered = filtered.filter(obj => obj.type === objectType);
        }

        // Filter by search term
        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            filtered = filtered.filter(obj => 
                obj.name.toLowerCase().includes(term) ||
                obj.schema.toLowerCase().includes(term)
            );
        }

        return filtered;
    }
}

module.exports = ObjectDiscovery;
