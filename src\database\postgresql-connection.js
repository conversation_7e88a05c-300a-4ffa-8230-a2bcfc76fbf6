const { Pool, Client } = require('pg');
const logger = require('../utils/logger');

class PostgreSQLConnection {
    constructor() {
        this.pool = null;
        this.config = null;
    }

    /**
     * Test database connection
     * @param {Object} config - Database configuration
     * @returns {Promise<boolean>} - Connection test result
     */
    async testConnection(config) {
        try {
            const testConfig = {
                host: config.host,
                port: config.port || 5432,
                database: config.database,
                user: config.username,
                password: config.password,
                connectionTimeoutMillis: 10000,
                query_timeout: 10000
            };

            const client = new Client(testConfig);
            await client.connect();
            await client.query('SELECT 1');
            await client.end();
            
            logger.info('PostgreSQL connection test successful');
            return true;
        } catch (error) {
            logger.error('PostgreSQL connection test failed:', error.message);
            throw new Error(`Connection failed: ${error.message}`);
        }
    }

    /**
     * Connect to database
     * @param {Object} config - Database configuration
     */
    async connect(config) {
        try {
            if (this.pool) {
                await this.disconnect();
            }

            this.config = {
                host: config.host,
                port: config.port || 5432,
                database: config.database,
                user: config.username,
                password: config.password,
                max: 10,
                min: 0,
                idleTimeoutMillis: 30000,
                connectionTimeoutMillis: 30000,
                query_timeout: 30000
            };

            this.pool = new Pool(this.config);
            
            // Test the connection
            const client = await this.pool.connect();
            await client.query('SELECT 1');
            client.release();
            
            logger.info('Connected to PostgreSQL database');
        } catch (error) {
            logger.error('Failed to connect to PostgreSQL:', error.message);
            throw error;
        }
    }

    /**
     * Disconnect from database
     */
    async disconnect() {
        try {
            if (this.pool) {
                await this.pool.end();
                this.pool = null;
                logger.info('Disconnected from PostgreSQL database');
            }
        } catch (error) {
            logger.error('Error disconnecting from PostgreSQL:', error.message);
        }
    }

    /**
     * Execute query
     * @param {string} query - SQL query
     * @param {Array} params - Query parameters
     * @returns {Promise<Object>} - Query result
     */
    async executeQuery(query, params = []) {
        try {
            if (!this.pool) {
                throw new Error('Not connected to database');
            }

            const client = await this.pool.connect();
            try {
                const result = await client.query(query, params);
                return result;
            } finally {
                client.release();
            }
        } catch (error) {
            logger.error('Query execution failed:', error.message);
            throw error;
        }
    }

    /**
     * Execute multiple queries in a transaction
     * @param {Array} queries - Array of SQL queries
     * @returns {Promise<Array>} - Array of query results
     */
    async executeTransaction(queries) {
        const client = await this.pool.connect();
        
        try {
            await client.query('BEGIN');
            const results = [];
            
            for (const query of queries) {
                const result = await client.query(query);
                results.push(result);
            }
            
            await client.query('COMMIT');
            return results;
        } catch (error) {
            await client.query('ROLLBACK');
            logger.error('Transaction failed:', error.message);
            throw error;
        } finally {
            client.release();
        }
    }

    /**
     * Get all database objects
     * @returns {Promise<Array>} - List of database objects
     */
    async getDatabaseObjects() {
        const query = `
            SELECT 
                schemaname as schema_name,
                tablename as object_name,
                'table' as object_type
            FROM pg_tables 
            WHERE schemaname NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
            
            UNION ALL
            
            SELECT 
                schemaname as schema_name,
                viewname as object_name,
                'view' as object_type
            FROM pg_views 
            WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
            
            UNION ALL
            
            SELECT 
                n.nspname as schema_name,
                p.proname as object_name,
                CASE 
                    WHEN p.prokind = 'f' THEN 'function'
                    WHEN p.prokind = 'p' THEN 'procedure'
                    ELSE 'function'
                END as object_type
            FROM pg_proc p
            JOIN pg_namespace n ON p.pronamespace = n.oid
            WHERE n.nspname NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
            
            ORDER BY schema_name, object_type, object_name
        `;

        try {
            const result = await this.executeQuery(query);
            return result.rows.map(row => ({
                schema: row.schema_name,
                name: row.object_name,
                type: row.object_type,
                created: null,
                modified: null
            }));
        } catch (error) {
            logger.error('Failed to get database objects:', error.message);
            throw error;
        }
    }

    /**
     * Get table structure
     * @param {string} schema - Schema name
     * @param {string} tableName - Table name
     * @returns {Promise<Array>} - Table structure
     */
    async getTableStructure(schema, tableName) {
        const query = `
            SELECT 
                c.column_name,
                c.data_type,
                c.character_maximum_length,
                c.numeric_precision,
                c.numeric_scale,
                c.is_nullable,
                c.column_default,
                CASE WHEN pk.column_name IS NOT NULL THEN true ELSE false END as is_primary_key,
                CASE WHEN fk.column_name IS NOT NULL THEN true ELSE false END as is_foreign_key,
                fk.foreign_table_schema,
                fk.foreign_table_name,
                fk.foreign_column_name
            FROM information_schema.columns c
            LEFT JOIN (
                SELECT ku.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage ku
                    ON tc.constraint_name = ku.constraint_name
                WHERE tc.table_schema = $1 
                    AND tc.table_name = $2
                    AND tc.constraint_type = 'PRIMARY KEY'
            ) pk ON c.column_name = pk.column_name
            LEFT JOIN (
                SELECT 
                    kcu.column_name,
                    ccu.table_schema AS foreign_table_schema,
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu
                    ON tc.constraint_name = kcu.constraint_name
                JOIN information_schema.constraint_column_usage ccu
                    ON ccu.constraint_name = tc.constraint_name
                WHERE tc.table_schema = $1 
                    AND tc.table_name = $2
                    AND tc.constraint_type = 'FOREIGN KEY'
            ) fk ON c.column_name = fk.column_name
            WHERE c.table_schema = $1 AND c.table_name = $2
            ORDER BY c.ordinal_position
        `;

        try {
            const result = await this.executeQuery(query, [schema, tableName]);
            return result.rows;
        } catch (error) {
            logger.error('Failed to get table structure:', error.message);
            throw error;
        }
    }

    /**
     * Check if table exists
     * @param {string} schema - Schema name
     * @param {string} tableName - Table name
     * @returns {Promise<boolean>} - Table existence
     */
    async tableExists(schema, tableName) {
        const query = `
            SELECT EXISTS (
                SELECT 1 
                FROM information_schema.tables 
                WHERE table_schema = $1 AND table_name = $2
            )
        `;

        try {
            const result = await this.executeQuery(query, [schema, tableName]);
            return result.rows[0].exists;
        } catch (error) {
            logger.error('Failed to check table existence:', error.message);
            throw error;
        }
    }

    /**
     * Create schema if not exists
     * @param {string} schemaName - Schema name
     */
    async createSchemaIfNotExists(schemaName) {
        const query = `CREATE SCHEMA IF NOT EXISTS "${schemaName}"`;
        
        try {
            await this.executeQuery(query);
            logger.info(`Schema "${schemaName}" created or already exists`);
        } catch (error) {
            logger.error('Failed to create schema:', error.message);
            throw error;
        }
    }

    /**
     * Check if connected
     * @returns {boolean} - Connection status
     */
    isConnected() {
        return this.pool && !this.pool.ended;
    }

    /**
     * Get database version
     * @returns {Promise<string>} - Database version
     */
    async getVersion() {
        try {
            const result = await this.executeQuery('SELECT version()');
            return result.rows[0].version;
        } catch (error) {
            logger.error('Failed to get database version:', error.message);
            throw error;
        }
    }
}

module.exports = PostgreSQLConnection;
