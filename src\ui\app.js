const { ipc<PERSON><PERSON><PERSON> } = require('electron');
const debug = require('debug')('mssql-migrator:ui');
const MSSQLConnection = require('../database/mssql-connection');
const PostgreSQLConnection = require('../database/postgresql-connection');
const ObjectDiscovery = require('../database/object-discovery');
const MigrationScriptGenerator = require('../database/migration-script-generator');

// Import jQuery
const $ = require('jquery');
// Make sure jQuery doesn't conflict with other libraries
$.noConflict();

class MigrationApp {
    constructor() {
        this.mssqlConnection = new MSSQLConnection();
        this.postgresConnection = new PostgreSQLConnection();
        this.objectDiscovery = new ObjectDiscovery(this.mssqlConnection);
        this.scriptGenerator = new MigrationScriptGenerator();
        
        this.discoveredObjects = [];
        this.selectedObjects = [];
        this.generatedScript = '';
        
        this.initializeEventListeners();
        this.loadSavedConnections();
    }

    /**
     * Initialize all event listeners
     */
    initializeEventListeners() {
        // Connection test buttons
        $('#btnTestSource').on('click', () => this.testSourceConnection());
        $('#btnTestTarget').on('click', () => this.testTargetConnection());
        
        // Main action buttons
        $('#btnDiscoverObjects').on('click', () => this.discoverObjects());
        $('#btnGenerateScript').on('click', () => this.generateScript());
        
        // Object management
        $('#selectAll').on('change', (e) => this.toggleSelectAll(e.target.checked));
        $('#searchObjects').on('input', (e) => this.searchObjects(e.target.value));
        $('#filterObjectType').on('change', (e) => this.filterObjects(e.target.value));
        $('#btnAddManual').on('click', () => this.showAddManualDialog());
        
        // Script actions
        $('#btnCopyScript').on('click', () => this.copyScript());
        $('#btnSaveScript').on('click', () => this.saveScript());
        $('#btnExecuteScript').on('click', () => this.executeScript());
        
        // Settings
        $('#btnSettings').on('click', () => this.showSettings());
        
        // Menu events from main process
        ipcRenderer.on('menu-new-migration', () => this.newMigration());
        ipcRenderer.on('menu-test-connections', () => this.testAllConnections());
        ipcRenderer.on('menu-refresh-objects', () => this.discoverObjects());
        ipcRenderer.on('menu-save-script', () => this.saveScript());
        
        // Tab switching
        $('#mainTabs button[data-bs-toggle="tab"]').on('shown.bs.tab', (e) => {
            const target = $(e.target).attr('data-bs-target');
            if (target === '#script') {
                this.refreshScriptTab();
            }
        });
    }

    /**
     * Test source database connection
     */
    async testSourceConnection() {
        const config = this.getSourceConfig();
        debug('Testing source connection with config: %O', {
            server: config.server,
            database: config.database,
            username: config.username,
            // ไม่แสดง password เลือก่อ
        });
        
        if (!this.validateSourceConfig(config)) {
            debug('Source config validation failed');
            return;
        }

        const btn = $('#btnTestSource');
        const originalText = btn.html();
        
        try {
            debug('Starting source connection test');
            btn.html('<i class="bi bi-hourglass-split"></i> Testing...').prop('disabled', true);
            this.updateStatus('Testing source connection...', 'testing');
            
            await this.mssqlConnection.testConnection(config);
            debug('Source connection test successful');
            
            this.showSuccess('Source connection test successful!');
            this.updateConnectionStatus('source', 'connected');
            this.updateStatus('Source connection test successful', 'connected');
            
            // Enable discover button if both connections are tested
            this.checkEnableDiscoverButton();
            
        } catch (error) {
            debug('Source connection test failed: %s', error.message);
            this.showError(`Source connection failed: ${error.message}`);
            this.updateConnectionStatus('source', 'disconnected');
            this.updateStatus('Source connection failed', 'disconnected');
        } finally {
            btn.html(originalText).prop('disabled', false);
        }
    }

    /**
     * Test target database connection
     */
    async testTargetConnection() {
        const config = this.getTargetConfig();
        if (!this.validateTargetConfig(config)) return;

        const btn = $('#btnTestTarget');
        const originalText = btn.html();
        
        try {
            btn.html('<i class="bi bi-hourglass-split"></i> Testing...').prop('disabled', true);
            this.updateStatus('Testing target connection...', 'testing');
            
            await this.postgresConnection.testConnection(config);
            
            this.showSuccess('Target connection test successful!');
            this.updateConnectionStatus('target', 'connected');
            this.updateStatus('Target connection test successful', 'connected');
            
            // Enable discover button if both connections are tested
            this.checkEnableDiscoverButton();
            
        } catch (error) {
            this.showError(`Target connection failed: ${error.message}`);
            this.updateConnectionStatus('target', 'disconnected');
            this.updateStatus('Target connection failed', 'disconnected');
        } finally {
            btn.html(originalText).prop('disabled', false);
        }
    }

    /**
     * Discover database objects
     */
    async discoverObjects() {
        const sourceConfig = this.getSourceConfig();
        if (!this.validateSourceConfig(sourceConfig)) return;

        this.showLoading('Discovering database objects...');
        
        try {
            // Connect to source database
            await this.mssqlConnection.connect(sourceConfig);
            
            // Discover objects
            this.discoveredObjects = await this.objectDiscovery.discoverAllObjects();
            
            // Update UI
            this.renderObjectsTable(this.discoveredObjects);
            this.updateStatus(`Discovered ${this.discoveredObjects.length} objects`, 'connected');
            
            // Enable generate script button
            $('#btnGenerateScript').prop('disabled', false);
            
            this.showSuccess(`Successfully discovered ${this.discoveredObjects.length} database objects!`);
            
        } catch (error) {
            this.showError(`Failed to discover objects: ${error.message}`);
            this.updateStatus('Object discovery failed', 'disconnected');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Generate migration script
     */
    async generateScript() {
        const selectedObjects = this.getSelectedObjects();
        
        if (selectedObjects.length === 0) {
            this.showWarning('Please select at least one object to migrate.');
            return;
        }

        this.showLoading('Generating migration script...');
        
        try {
            this.generatedScript = await this.scriptGenerator.generateMigrationScript(
                selectedObjects,
                this.mssqlConnection
            );
            
            $('#scriptContent').val(this.generatedScript);
            
            // Switch to script tab
            $('#script-tab').tab('show');
            
            // Enable execute button if target connection is available
            const targetConfig = this.getTargetConfig();
            if (this.validateTargetConfig(targetConfig, false)) {
                $('#btnExecuteScript').prop('disabled', false);
            }
            
            this.updateStatus(`Generated script for ${selectedObjects.length} objects`, 'connected');
            this.showSuccess('Migration script generated successfully!');
            
        } catch (error) {
            this.showError(`Failed to generate script: ${error.message}`);
            this.updateStatus('Script generation failed', 'disconnected');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Get source database configuration
     */
    getSourceConfig() {
        return {
            server: $('#sourceServer').val().trim(),
            database: $('#sourceDatabase').val().trim(),
            username: $('#sourceUsername').val().trim(),
            password: $('#sourcePassword').val()
        };
    }

    /**
     * Get target database configuration
     */
    getTargetConfig() {
        return {
            host: $('#targetHost').val().trim(),
            port: parseInt($('#targetPort').val()) || 5432,
            database: $('#targetDatabase').val().trim(),
            username: $('#targetUsername').val().trim(),
            password: $('#targetPassword').val()
        };
    }

    /**
     * Validate source configuration
     */
    validateSourceConfig(config, showError = true) {
        const required = ['server', 'database', 'username'];
        const missing = required.filter(field => !config[field]);
        
        if (missing.length > 0) {
            if (showError) {
                this.showError(`Please fill in required source fields: ${missing.join(', ')}`);
            }
            return false;
        }
        return true;
    }

    /**
     * Validate target configuration
     */
    validateTargetConfig(config, showError = true) {
        const required = ['host', 'database', 'username'];
        const missing = required.filter(field => !config[field]);
        
        if (missing.length > 0) {
            if (showError) {
                this.showError(`Please fill in required target fields: ${missing.join(', ')}`);
            }
            return false;
        }
        return true;
    }

    /**
     * Update status bar
     */
    updateStatus(message, type = 'info') {
        $('#statusText').text(message);
        
        const statusClasses = 'text-success text-danger text-warning';
        $('#statusText').removeClass(statusClasses);
        
        switch (type) {
            case 'connected':
            case 'success':
                $('#statusText').addClass('text-success');
                break;
            case 'disconnected':
            case 'error':
                $('#statusText').addClass('text-danger');
                break;
            case 'testing':
            case 'warning':
                $('#statusText').addClass('text-warning');
                break;
        }
    }

    /**
     * Update connection status
     */
    updateConnectionStatus(type, status) {
        const statusText = status === 'connected' ? 'Connected' : 'Not Connected';
        const statusClass = status === 'connected' ? 'text-success' : 'text-danger';
        
        $('#connectionStatus')
            .text(`${type.charAt(0).toUpperCase() + type.slice(1)}: ${statusText}`)
            .removeClass('text-success text-danger')
            .addClass(statusClass);
    }

    /**
     * Check if discover button should be enabled
     */
    checkEnableDiscoverButton() {
        const sourceConfig = this.getSourceConfig();
        const isSourceValid = this.validateSourceConfig(sourceConfig, false);
        
        $('#btnDiscoverObjects').prop('disabled', !isSourceValid);
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        this.showToast(message, 'success');
    }

    /**
     * Show error message
     */
    showError(message) {
        this.showToast(message, 'error');
    }

    /**
     * Show warning message
     */
    showWarning(message) {
        this.showToast(message, 'warning');
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info') {
        // Create toast element
        const toastId = 'toast-' + Date.now();
        const bgClass = type === 'success' ? 'bg-success' : 
                       type === 'error' ? 'bg-danger' : 
                       type === 'warning' ? 'bg-warning' : 'bg-primary';
        
        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;
        
        // Add to container or create one
        let container = $('#toast-container');
        if (container.length === 0) {
            container = $('<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;"></div>');
            $('body').append(container);
        }
        
        container.append(toastHtml);
        
        // Show toast
        const toast = new bootstrap.Toast(document.getElementById(toastId));
        toast.show();
        
        // Remove from DOM after hiding
        $(`#${toastId}`).on('hidden.bs.toast', function() {
            $(this).remove();
        });
    }

    /**
     * Show loading modal
     */
    showLoading(message = 'Processing...') {
        $('#loadingText').text(message);
        $('#loadingModal').modal('show');
    }

    /**
     * Hide loading modal
     */
    hideLoading() {
        $('#loadingModal').modal('hide');
    }

    /**
     * Render objects table
     */
    renderObjectsTable(objects) {
        const tbody = $('#objectsTableBody');
        tbody.empty();

        if (objects.length === 0) {
            tbody.append(`
                <tr>
                    <td colspan="6" class="text-center text-muted py-5">
                        <i class="bi bi-database display-4"></i>
                        <br>
                        No objects found
                    </td>
                </tr>
            `);
            return;
        }

        objects.forEach((obj, index) => {
            const statusBadge = this.getStatusBadge(obj.status);
            const typeBadge = this.getTypeBadge(obj.type);
            const rowCount = obj.rowCount !== null ? obj.rowCount.toLocaleString() : '-';

            const row = `
                <tr data-object-index="${index}">
                    <td>
                        <input type="checkbox" class="form-check-input object-checkbox"
                               data-index="${index}" ${obj.selected ? 'checked' : ''}>
                    </td>
                    <td>
                        <strong>${obj.name}</strong>
                        ${obj.rowCount !== null ? `<br><small class="text-muted">${rowCount} rows</small>` : ''}
                    </td>
                    <td>${typeBadge}</td>
                    <td>${obj.schema}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-secondary" onclick="app.viewObjectDetails(${index})" title="View Details">
                            <i class="bi bi-eye"></i>
                        </button>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });

        // Add event listeners for checkboxes
        $('.object-checkbox').on('change', (e) => {
            const index = parseInt(e.target.dataset.index);
            this.discoveredObjects[index].selected = e.target.checked;
            this.updateSelectAllCheckbox();
        });
    }

    /**
     * Get status badge HTML
     */
    getStatusBadge(status) {
        const badges = {
            'ready': '<span class="badge bg-success">Ready</span>',
            'error': '<span class="badge bg-danger">Error</span>',
            'migrated': '<span class="badge bg-primary">Migrated</span>'
        };
        return badges[status] || '<span class="badge bg-secondary">Unknown</span>';
    }

    /**
     * Get type badge HTML
     */
    getTypeBadge(type) {
        const badges = {
            'table': '<span class="badge badge-table">Table</span>',
            'view': '<span class="badge badge-view">View</span>',
            'procedure': '<span class="badge badge-procedure">Procedure</span>',
            'function': '<span class="badge badge-function">Function</span>'
        };
        return badges[type] || '<span class="badge bg-secondary">Unknown</span>';
    }

    /**
     * Toggle select all objects
     */
    toggleSelectAll(checked) {
        this.discoveredObjects.forEach(obj => obj.selected = checked);
        $('.object-checkbox').prop('checked', checked);
    }

    /**
     * Update select all checkbox state
     */
    updateSelectAllCheckbox() {
        const total = this.discoveredObjects.length;
        const selected = this.discoveredObjects.filter(obj => obj.selected).length;

        const selectAllCheckbox = $('#selectAll');
        selectAllCheckbox.prop('checked', selected === total);
        selectAllCheckbox.prop('indeterminate', selected > 0 && selected < total);
    }

    /**
     * Search objects
     */
    searchObjects(searchTerm) {
        const filtered = this.objectDiscovery.searchObjects(
            this.discoveredObjects,
            searchTerm,
            $('#filterObjectType').val()
        );
        this.renderObjectsTable(filtered);
    }

    /**
     * Filter objects by type
     */
    filterObjects(objectType) {
        const filtered = this.objectDiscovery.searchObjects(
            this.discoveredObjects,
            $('#searchObjects').val(),
            objectType
        );
        this.renderObjectsTable(filtered);
    }

    /**
     * Get selected objects
     */
    getSelectedObjects() {
        return this.discoveredObjects.filter(obj => obj.selected);
    }

    /**
     * Copy script to clipboard
     */
    async copyScript() {
        const script = $('#scriptContent').val();
        if (!script) {
            this.showWarning('No script to copy');
            return;
        }

        try {
            await navigator.clipboard.writeText(script);
            this.showSuccess('Script copied to clipboard!');
        } catch (error) {
            this.showError('Failed to copy script to clipboard');
        }
    }

    /**
     * Save script to file
     */
    async saveScript() {
        const script = $('#scriptContent').val();
        if (!script) {
            this.showWarning('No script to save');
            return;
        }

        try {
            const result = await ipcRenderer.invoke('show-save-dialog', {
                title: 'Save Migration Script',
                defaultPath: 'migration-script.sql',
                filters: [
                    { name: 'SQL Files', extensions: ['sql'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });

            if (!result.canceled) {
                const fs = require('fs');
                fs.writeFileSync(result.filePath, script, 'utf8');
                this.showSuccess('Script saved successfully!');
            }
        } catch (error) {
            this.showError(`Failed to save script: ${error.message}`);
        }
    }

    /**
     * Execute migration script
     */
    async executeScript() {
        const script = $('#scriptContent').val();
        if (!script) {
            this.showWarning('No script to execute');
            return;
        }

        const targetConfig = this.getTargetConfig();
        if (!this.validateTargetConfig(targetConfig)) return;

        // Confirm execution
        const result = await ipcRenderer.invoke('show-message-box', {
            type: 'warning',
            buttons: ['Execute', 'Cancel'],
            defaultId: 1,
            title: 'Confirm Migration',
            message: 'Are you sure you want to execute the migration script?',
            detail: 'This will modify the target database. Make sure you have a backup.'
        });

        if (result.response !== 0) return;

        this.showLoading('Executing migration script...');

        try {
            // Connect to target database
            await this.postgresConnection.connect(targetConfig);

            // Split script into individual statements
            const statements = script.split(';').filter(stmt => stmt.trim());

            // Execute statements
            for (let i = 0; i < statements.length; i++) {
                const statement = statements[i].trim();
                if (statement) {
                    this.showLoading(`Executing statement ${i + 1} of ${statements.length}...`);
                    await this.postgresConnection.executeQuery(statement);
                }
            }

            this.showSuccess('Migration script executed successfully!');
            this.updateStatus('Migration completed successfully', 'success');

        } catch (error) {
            this.showError(`Migration failed: ${error.message}`);
            this.updateStatus('Migration failed', 'error');
        } finally {
            this.hideLoading();
            await this.postgresConnection.disconnect();
        }
    }

    /**
     * Load saved connections from localStorage
     */
    loadSavedConnections() {
        try {
            const saved = localStorage.getItem('migrationConnections');
            if (saved) {
                const connections = JSON.parse(saved);

                // Load source connection
                if (connections.source) {
                    $('#sourceServer').val(connections.source.server || '');
                    $('#sourceDatabase').val(connections.source.database || '');
                    $('#sourceUsername').val(connections.source.username || '');
                }

                // Load target connection
                if (connections.target) {
                    $('#targetHost').val(connections.target.host || '');
                    $('#targetPort').val(connections.target.port || 5432);
                    $('#targetDatabase').val(connections.target.database || '');
                    $('#targetUsername').val(connections.target.username || '');
                }
            }
        } catch (error) {
            console.warn('Failed to load saved connections:', error);
        }
    }

    /**
     * Save connections to localStorage
     */
    saveConnections() {
        try {
            const connections = {
                source: {
                    server: $('#sourceServer').val(),
                    database: $('#sourceDatabase').val(),
                    username: $('#sourceUsername').val()
                },
                target: {
                    host: $('#targetHost').val(),
                    port: $('#targetPort').val(),
                    database: $('#targetDatabase').val(),
                    username: $('#targetUsername').val()
                }
            };

            localStorage.setItem('migrationConnections', JSON.stringify(connections));
        } catch (error) {
            console.warn('Failed to save connections:', error);
        }
    }

    /**
     * New migration - reset everything
     */
    newMigration() {
        this.discoveredObjects = [];
        this.selectedObjects = [];
        this.generatedScript = '';

        $('#objectsTableBody').html(`
            <tr>
                <td colspan="6" class="text-center text-muted py-5">
                    <i class="bi bi-database display-4"></i>
                    <br>
                    Click "Discover Objects" to load database objects
                </td>
            </tr>
        `);

        $('#scriptContent').val('');
        $('#btnGenerateScript').prop('disabled', true);
        $('#btnExecuteScript').prop('disabled', true);

        // Switch to objects tab
        $('#objects-tab').tab('show');

        this.updateStatus('Ready for new migration', 'info');
        this.showSuccess('Ready for new migration');
    }

    /**
     * Test all connections
     */
    async testAllConnections() {
        await this.testSourceConnection();
        await this.testTargetConnection();
    }

    /**
     * Refresh script tab
     */
    refreshScriptTab() {
        // Auto-save connections when switching to script tab
        this.saveConnections();
    }

    /**
     * Show add manual object dialog
     */
    showAddManualDialog() {
        // This would show a modal to manually add objects
        // For now, just show a simple prompt
        const objectName = prompt('Enter object name (schema.objectname):');
        if (objectName && objectName.includes('.')) {
            const [schema, name] = objectName.split('.');
            const manualObject = {
                schema: schema.trim(),
                name: name.trim(),
                type: 'table', // Default to table
                selected: true,
                status: 'ready',
                rowCount: null,
                created: null,
                modified: null
            };

            this.discoveredObjects.push(manualObject);
            this.renderObjectsTable(this.discoveredObjects);
            this.showSuccess(`Added manual object: ${objectName}`);
        }
    }

    /**
     * View object details
     */
    viewObjectDetails(index) {
        const obj = this.discoveredObjects[index];
        if (!obj) return;

        // Create a simple details modal
        const details = `
            <strong>Object:</strong> ${obj.schema}.${obj.name}<br>
            <strong>Type:</strong> ${obj.type}<br>
            <strong>Status:</strong> ${obj.status}<br>
            ${obj.rowCount !== null ? `<strong>Rows:</strong> ${obj.rowCount.toLocaleString()}<br>` : ''}
            ${obj.created ? `<strong>Created:</strong> ${new Date(obj.created).toLocaleString()}<br>` : ''}
            ${obj.modified ? `<strong>Modified:</strong> ${new Date(obj.modified).toLocaleString()}<br>` : ''}
        `;

        // Show in a simple alert for now (could be enhanced with a proper modal)
        alert(`Object Details:\n\n${details.replace(/<br>/g, '\n').replace(/<strong>|<\/strong>/g, '')}`);
    }

    /**
     * Show settings dialog
     */
    showSettings() {
        this.showSuccess('Settings feature coming soon!');
    }
}

// Initialize the application when DOM is ready
$(document).ready(() => {
    window.app = new MigrationApp();

    // Auto-save connections on input change
    $('input[id^="source"], input[id^="target"]').on('change', () => {
        window.app.saveConnections();
    });
});

