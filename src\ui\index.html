<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MSSQL to PostgreSQL Migrator</title>
    
    <!-- Bootstrap CSS -->
    <link href="../../node_modules/bootstrap/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="../../node_modules/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid h-100">
        <!-- Header -->
        <div class="row bg-primary text-white py-2">
            <div class="col">
                <h4 class="mb-0">
                    <i class="bi bi-database-gear me-2"></i>
                    MSSQL to PostgreSQL Migrator
                </h4>
            </div>
            <div class="col-auto">
                <button class="btn btn-outline-light btn-sm" id="btnSettings">
                    <i class="bi bi-gear"></i> Settings
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="row flex-fill">
            <!-- Left Panel - Database Connections -->
            <div class="col-md-4 border-end bg-light">
                <div class="p-3">
                    <h5 class="mb-3">Database Connections</h5>
                    
                    <!-- Source Database -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-database me-2"></i>
                                Source Database (MSSQL)
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <label class="form-label">Server</label>
                                <input type="text" class="form-control form-control-sm" id="sourceServer" placeholder="localhost">
                            </div>
                            <div class="mb-2">
                                <label class="form-label">Database</label>
                                <input type="text" class="form-control form-control-sm" id="sourceDatabase" placeholder="Database name">
                            </div>
                            <div class="mb-2">
                                <label class="form-label">Username</label>
                                <input type="text" class="form-control form-control-sm" id="sourceUsername" placeholder="Username">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Password</label>
                                <input type="password" class="form-control form-control-sm" id="sourcePassword" placeholder="Password">
                            </div>
                            <button class="btn btn-outline-primary btn-sm w-100" id="btnTestSource">
                                <i class="bi bi-plug"></i> Test Connection
                            </button>
                        </div>
                    </div>

                    <!-- Target Database -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-database-add me-2"></i>
                                Target Database (PostgreSQL)
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <label class="form-label">Host</label>
                                <input type="text" class="form-control form-control-sm" id="targetHost" placeholder="localhost">
                            </div>
                            <div class="mb-2">
                                <label class="form-label">Port</label>
                                <input type="number" class="form-control form-control-sm" id="targetPort" placeholder="5432" value="5432">
                            </div>
                            <div class="mb-2">
                                <label class="form-label">Database</label>
                                <input type="text" class="form-control form-control-sm" id="targetDatabase" placeholder="Database name">
                            </div>
                            <div class="mb-2">
                                <label class="form-label">Username</label>
                                <input type="text" class="form-control form-control-sm" id="targetUsername" placeholder="Username">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Password</label>
                                <input type="password" class="form-control form-control-sm" id="targetPassword" placeholder="Password">
                            </div>
                            <button class="btn btn-outline-success btn-sm w-100" id="btnTestTarget">
                                <i class="bi bi-plug"></i> Test Connection
                            </button>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" id="btnDiscoverObjects">
                            <i class="bi bi-search"></i> Discover Objects
                        </button>
                        <button class="btn btn-success" id="btnGenerateScript" disabled>
                            <i class="bi bi-file-earmark-code"></i> Generate Script
                        </button>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Objects and Scripts -->
            <div class="col-md-8">
                <!-- Tabs -->
                <ul class="nav nav-tabs mt-3" id="mainTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="objects-tab" data-bs-toggle="tab" data-bs-target="#objects" type="button" role="tab">
                            <i class="bi bi-list-ul"></i> Database Objects
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="script-tab" data-bs-toggle="tab" data-bs-target="#script" type="button" role="tab">
                            <i class="bi bi-file-earmark-code"></i> Generated Script
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="mainTabContent">
                    <!-- Objects Tab -->
                    <div class="tab-pane fade show active" id="objects" role="tabpanel">
                        <div class="p-3">
                            <!-- Search and Filter -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-search"></i>
                                        </span>
                                        <input type="text" class="form-control" id="searchObjects" placeholder="Search objects...">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="filterObjectType">
                                        <option value="">All Types</option>
                                        <option value="table">Tables</option>
                                        <option value="view">Views</option>
                                        <option value="procedure">Procedures</option>
                                        <option value="function">Functions</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-outline-secondary w-100" id="btnAddManual">
                                        <i class="bi bi-plus"></i> Add Manual
                                    </button>
                                </div>
                            </div>

                            <!-- Objects Table -->
                            <div class="table-responsive" style="height: 500px;">
                                <table class="table table-sm table-hover">
                                    <thead class="table-dark sticky-top">
                                        <tr>
                                            <th width="50">
                                                <input type="checkbox" id="selectAll" class="form-check-input">
                                            </th>
                                            <th>Object Name</th>
                                            <th>Type</th>
                                            <th>Schema</th>
                                            <th>Status</th>
                                            <th width="100">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="objectsTableBody">
                                        <tr>
                                            <td colspan="6" class="text-center text-muted py-5">
                                                <i class="bi bi-database display-4"></i>
                                                <br>
                                                Click "Discover Objects" to load database objects
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Script Tab -->
                    <div class="tab-pane fade" id="script" role="tabpanel">
                        <div class="p-3">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">Generated Migration Script</h6>
                                <div>
                                    <button class="btn btn-outline-secondary btn-sm me-2" id="btnCopyScript">
                                        <i class="bi bi-clipboard"></i> Copy
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm me-2" id="btnSaveScript">
                                        <i class="bi bi-download"></i> Save
                                    </button>
                                    <button class="btn btn-warning btn-sm" id="btnExecuteScript" disabled>
                                        <i class="bi bi-play-fill"></i> Execute
                                    </button>
                                </div>
                            </div>
                            <textarea class="form-control font-monospace" id="scriptContent" rows="25" readonly placeholder="Generated script will appear here..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Bar -->
        <div class="row bg-secondary text-white py-1">
            <div class="col">
                <small id="statusText">Ready</small>
            </div>
            <div class="col-auto">
                <small id="connectionStatus">Not Connected</small>
            </div>
        </div>
    </div>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div id="loadingText">Processing...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../../node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../node_modules/jquery/dist/jquery.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
