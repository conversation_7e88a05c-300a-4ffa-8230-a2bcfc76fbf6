/* Custom Styles for MSSQL to PostgreSQL Migrator */

html, body {
    height: 100%;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.container-fluid {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.row.flex-fill {
    flex: 1;
    overflow: hidden;
}

/* Header Styles */
.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

/* Card Styles */
.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Form Styles */
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control-sm {
    font-size: 0.875rem;
}

/* Button Styles */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-outline-success:hover {
    background-color: #28a745;
    border-color: #28a745;
}

/* Table Styles */
.table {
    margin-bottom: 0;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.table th {
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table td {
    vertical-align: middle;
    font-size: 0.875rem;
}

/* Tab Styles */
.nav-tabs {
    border-bottom: 2px solid #dee2e6;
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
    font-weight: 500;
    padding: 12px 20px;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.nav-tabs .nav-link.active {
    color: #007bff;
    background-color: white;
    border-color: #dee2e6 #dee2e6 #fff;
    border-bottom: 2px solid #007bff;
}

/* Status Styles */
.status-connected {
    color: #28a745 !important;
}

.status-disconnected {
    color: #dc3545 !important;
}

.status-testing {
    color: #ffc107 !important;
}

/* Object Type Badges */
.badge-table {
    background-color: #007bff;
}

.badge-view {
    background-color: #28a745;
}

.badge-procedure {
    background-color: #ffc107;
    color: #212529;
}

.badge-function {
    background-color: #17a2b8;
}

/* Scrollable Areas */
.table-responsive {
    border: 1px solid #dee2e6;
    border-radius: 6px;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Code/Script Area */
.font-monospace {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.4;
}

#scriptContent {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    resize: none;
}

/* Search Input */
.input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
}

/* Custom Checkbox */
.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .col-md-4 {
        border-bottom: 1px solid #dee2e6;
        border-right: none;
    }
    
    .table-responsive {
        height: 300px !important;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateY(-10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Success/Error States */
.border-success {
    border-color: #28a745 !important;
}

.border-danger {
    border-color: #dc3545 !important;
}

.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

/* Utility Classes */
.cursor-pointer {
    cursor: pointer;
}

.user-select-none {
    user-select: none;
}

/* Dark Mode Support (Optional) */
@media (prefers-color-scheme: dark) {
    .bg-light {
        background-color: #343a40 !important;
        color: white;
    }
    
    .card {
        background-color: #495057;
        color: white;
    }
    
    .table {
        color: white;
    }
    
    .form-control {
        background-color: #495057;
        border-color: #6c757d;
        color: white;
    }
}
