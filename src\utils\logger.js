const winston = require('winston');
const path = require('path');

// Create logs directory if it doesn't exist
const fs = require('fs');
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
}

// Define log format
const logFormat = winston.format.combine(
    winston.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss'
    }),
    winston.format.errors({ stack: true }),
    winston.format.printf(({ timestamp, level, message, stack }) => {
        return `${timestamp} [${level.toUpperCase()}]: ${stack || message}`;
    })
);

// Create logger instance
const logger = winston.createLogger({
    level: 'info',
    format: logFormat,
    transports: [
        // Write all logs to console
        new winston.transports.Console({
            format: winston.format.combine(
                winston.format.colorize(),
                logFormat
            )
        }),
        
        // Write all logs to file
        new winston.transports.File({
            filename: path.join(logsDir, 'app.log'),
            maxsize: 5242880, // 5MB
            maxFiles: 5
        }),
        
        // Write error logs to separate file
        new winston.transports.File({
            filename: path.join(logsDir, 'error.log'),
            level: 'error',
            maxsize: 5242880, // 5MB
            maxFiles: 5
        })
    ]
});

// Add migration-specific logging methods
logger.migration = {
    start: (operation) => {
        logger.info(`🚀 Starting migration operation: ${operation}`);
    },
    
    success: (operation) => {
        logger.info(`✅ Migration operation completed successfully: ${operation}`);
    },
    
    error: (operation, error) => {
        logger.error(`❌ Migration operation failed: ${operation} - ${error.message}`);
    },
    
    progress: (message) => {
        logger.info(`⏳ ${message}`);
    },
    
    warning: (message) => {
        logger.warn(`⚠️ ${message}`);
    }
};

module.exports = logger;
