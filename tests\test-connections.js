const MSSQLConnection = require('../src/database/mssql-connection');
const PostgreSQLConnection = require('../src/database/postgresql-connection');

/**
 * Test database connections
 * This is a simple test script to verify database connectivity
 */

async function testMSSQLConnection() {
    console.log('Testing MSSQL Connection...');
    
    const mssql = new MSSQLConnection();
    
    const config = {
        server: 'localhost',
        database: 'TestDB',
        username: 'sa',
        password: 'YourPassword123'
    };
    
    try {
        const result = await mssql.testConnection(config);
        console.log('✅ MSSQL Connection: SUCCESS');
        return true;
    } catch (error) {
        console.log('❌ MSSQL Connection: FAILED');
        console.log('Error:', error.message);
        return false;
    }
}

async function testPostgreSQLConnection() {
    console.log('Testing PostgreSQL Connection...');
    
    const postgres = new PostgreSQLConnection();
    
    const config = {
        host: 'localhost',
        port: 5432,
        database: 'testdb',
        username: 'postgres',
        password: 'YourPassword123'
    };
    
    try {
        const result = await postgres.testConnection(config);
        console.log('✅ PostgreSQL Connection: SUCCESS');
        return true;
    } catch (error) {
        console.log('❌ PostgreSQL Connection: FAILED');
        console.log('Error:', error.message);
        return false;
    }
}

async function testObjectDiscovery() {
    console.log('Testing Object Discovery...');
    
    const mssql = new MSSQLConnection();
    const ObjectDiscovery = require('../src/database/object-discovery');
    
    const config = {
        server: 'localhost',
        database: 'TestDB',
        username: 'sa',
        password: 'YourPassword123'
    };
    
    try {
        await mssql.connect(config);
        const discovery = new ObjectDiscovery(mssql);
        const objects = await discovery.discoverAllObjects();
        
        console.log(`✅ Object Discovery: SUCCESS (Found ${objects.length} objects)`);
        
        // Display object summary
        const summary = objects.reduce((acc, obj) => {
            acc[obj.type] = (acc[obj.type] || 0) + 1;
            return acc;
        }, {});
        
        console.log('Object Summary:', summary);
        
        await mssql.disconnect();
        return true;
    } catch (error) {
        console.log('❌ Object Discovery: FAILED');
        console.log('Error:', error.message);
        return false;
    }
}

async function testScriptGeneration() {
    console.log('Testing Script Generation...');
    
    const MigrationScriptGenerator = require('../src/database/migration-script-generator');
    const generator = new MigrationScriptGenerator();
    
    // Mock objects for testing
    const mockObjects = [
        {
            schema: 'dbo',
            name: 'TestTable',
            type: 'table',
            selected: true,
            status: 'ready'
        },
        {
            schema: 'dbo',
            name: 'TestView',
            type: 'view',
            selected: true,
            status: 'ready'
        }
    ];
    
    try {
        // This would normally require a real MSSQL connection
        // For testing, we'll just test the header/footer generation
        const header = generator.generateScriptHeader();
        const footer = generator.generateScriptFooter();
        
        console.log('✅ Script Generation: SUCCESS');
        console.log('Header length:', header.length);
        console.log('Footer length:', footer.length);
        return true;
    } catch (error) {
        console.log('❌ Script Generation: FAILED');
        console.log('Error:', error.message);
        return false;
    }
}

async function runAllTests() {
    console.log('='.repeat(50));
    console.log('MSSQL to PostgreSQL Migrator - Connection Tests');
    console.log('='.repeat(50));
    console.log();
    
    const results = [];
    
    // Test MSSQL Connection
    results.push(await testMSSQLConnection());
    console.log();
    
    // Test PostgreSQL Connection
    results.push(await testPostgreSQLConnection());
    console.log();
    
    // Test Object Discovery (requires MSSQL connection)
    results.push(await testObjectDiscovery());
    console.log();
    
    // Test Script Generation
    results.push(await testScriptGeneration());
    console.log();
    
    // Summary
    console.log('='.repeat(50));
    console.log('Test Summary:');
    console.log('='.repeat(50));
    
    const passed = results.filter(r => r).length;
    const total = results.length;
    
    console.log(`Passed: ${passed}/${total}`);
    
    if (passed === total) {
        console.log('🎉 All tests passed!');
    } else {
        console.log('⚠️  Some tests failed. Please check your database connections.');
    }
    
    console.log();
    console.log('Note: Update the connection configurations in this file');
    console.log('to match your actual database settings before running tests.');
}

// Run tests if this file is executed directly
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = {
    testMSSQLConnection,
    testPostgreSQLConnection,
    testObjectDiscovery,
    testScriptGeneration,
    runAllTests
};
